#!/bin/bash

# Android Build Script for Colony
# This script automatically configures the environment and builds Colony for Android

set -e

echo "🏗️  Colony Android Build Script"
echo "================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists cargo; then
    echo "❌ Rust/Cargo not found. Please install Rust first."
    exit 1
fi

if ! command_exists rustup; then
    echo "❌ rustup not found. Please install Rust via rustup."
    exit 1
fi

# Check if Android targets are installed
echo "📱 Checking Android targets..."
if ! rustup target list --installed | grep -q "aarch64-linux-android"; then
    echo "📦 Installing Android targets..."
    rustup target add aarch64-linux-android
    rustup target add armv7-linux-androideabi
    rustup target add i686-linux-android
    rustup target add x86_64-linux-android
else
    echo "✅ Android targets already installed"
fi

# Check Android SDK
if [ -z "$ANDROID_HOME" ]; then
    echo "❌ ANDROID_HOME not set. Trying to auto-detect..."

    # Common SDK locations
    POSSIBLE_SDKS=(
        "$HOME/Android/Sdk"
        "$HOME/Library/Android/sdk"
        "/opt/android-sdk"
        "/usr/local/android-sdk"
    )

    for sdk_path in "${POSSIBLE_SDKS[@]}"; do
        if [ -d "$sdk_path" ]; then
            export ANDROID_HOME="$sdk_path"
            echo "🎯 Auto-detected SDK: $ANDROID_HOME"
            break
        fi
    done

    if [ -z "$ANDROID_HOME" ]; then
        echo "❌ Could not find Android SDK. Please install it and set ANDROID_HOME"
        echo ""
        echo "Install options:"
        echo "1. Android Studio: Tools → SDK Manager"
        echo "2. Command line tools: https://developer.android.com/studio#command-tools"
        echo ""
        echo "Then run: export ANDROID_HOME=/path/to/sdk"
        exit 1
    fi
fi

# Check Android NDK
if [ -z "$ANDROID_NDK_ROOT" ]; then
    echo "❌ ANDROID_NDK_ROOT not set. Trying to auto-detect..."

    # Try to find NDK within the detected SDK first
    if [ -n "$ANDROID_HOME" ] && [ -d "$ANDROID_HOME/ndk" ]; then
        latest_ndk=$(find "$ANDROID_HOME/ndk" -maxdepth 1 -type d -name "*.*.*" | sort -V | tail -1)
        if [ -n "$latest_ndk" ]; then
            export ANDROID_NDK_ROOT="$latest_ndk"
            echo "🎯 Auto-detected NDK: $ANDROID_NDK_ROOT"
        fi
    fi

    # If not found in SDK, try common locations
    if [ -z "$ANDROID_NDK_ROOT" ]; then
        POSSIBLE_NDKS=(
            "$HOME/Android/Sdk/ndk"
            "$HOME/Library/Android/sdk/ndk"
            "/opt/android-ndk"
            "/usr/local/android-ndk"
        )

        for ndk_base in "${POSSIBLE_NDKS[@]}"; do
            if [ -d "$ndk_base" ]; then
                # Find the latest version
                latest_ndk=$(find "$ndk_base" -maxdepth 1 -type d -name "*.*.*" | sort -V | tail -1)
                if [ -n "$latest_ndk" ]; then
                    export ANDROID_NDK_ROOT="$latest_ndk"
                    echo "🎯 Auto-detected NDK: $ANDROID_NDK_ROOT"
                    break
                fi
            fi
        done
    fi

    if [ -z "$ANDROID_NDK_ROOT" ]; then
        echo "❌ Could not find Android NDK. Please install it and set ANDROID_NDK_ROOT"
        echo ""
        echo "Install options:"
        echo "1. Android Studio: Tools → SDK Manager → SDK Tools → NDK"
        echo "2. Command line: https://developer.android.com/ndk/downloads"
        echo ""
        echo "Then run: export ANDROID_NDK_ROOT=/path/to/ndk"
        exit 1
    fi
fi

if [ ! -d "$ANDROID_NDK_ROOT" ]; then
    echo "❌ NDK directory not found: $ANDROID_NDK_ROOT"
    exit 1
fi

echo "✅ Using Android NDK: $ANDROID_NDK_ROOT"

# Set NDK_HOME for Tauri (some tools expect this instead of ANDROID_NDK_ROOT)
export NDK_HOME="$ANDROID_NDK_ROOT"

# Set up toolchain environment
TOOLCHAIN_DIR="$ANDROID_NDK_ROOT/toolchains/llvm/prebuilt/linux-x86_64"
API_LEVEL=24

if [ ! -d "$TOOLCHAIN_DIR" ]; then
    echo "❌ Toolchain not found: $TOOLCHAIN_DIR"
    echo "Your NDK installation might be incomplete"
    exit 1
fi

echo "🔧 Configuring build environment..."

# Export environment variables for RocksDB compilation
export PATH="$TOOLCHAIN_DIR/bin:$PATH"

# ARM64 configuration
export CC_aarch64_linux_android="$TOOLCHAIN_DIR/bin/aarch64-linux-android${API_LEVEL}-clang"
export CXX_aarch64_linux_android="$TOOLCHAIN_DIR/bin/aarch64-linux-android${API_LEVEL}-clang++"
export AR_aarch64_linux_android="$TOOLCHAIN_DIR/bin/llvm-ar"
export CARGO_TARGET_AARCH64_LINUX_ANDROID_LINKER="$TOOLCHAIN_DIR/bin/aarch64-linux-android${API_LEVEL}-clang"

# Additional environment variables for RocksDB
export CFLAGS_aarch64_linux_android="-D__ANDROID_API__=${API_LEVEL}"
export CXXFLAGS_aarch64_linux_android="-D__ANDROID_API__=${API_LEVEL}"

# Force rustls usage to avoid OpenSSL cross-compilation issues
export CARGO_FEATURE_RUSTLS_TLS=1
export CARGO_FEATURE_NATIVE_TLS=0

# Verify compiler exists
if [ ! -f "$CC_aarch64_linux_android" ]; then
    echo "❌ Android compiler not found: $CC_aarch64_linux_android"
    echo "Your NDK installation might be incomplete or for a different platform"
    exit 1
fi

echo "✅ Build environment configured"

# Build target (default to ARM64)
TARGET=${1:-aarch64-linux-android}

echo "🎯 Building for target: $TARGET"

# Change to src-tauri directory
cd src-tauri

echo "🚀 Starting build..."

# Build with verbose output to see any issues
#RUST_LOG=debug cargo build --target "$TARGET" --verbose
#RUST_LOG=debug cargo tauri android dev
cargo tauri android build --apk --target aarch64

# sign release
cd ../certs/android

KEYSTORE_PATH="colony-release.keystore"
KEY_ALIAS="colony-release"
APK_PATH="../../src-tauri/gen/android/app/build/outputs/apk/universal/release/app-universal-release-unsigned.apk"
OUTPUT_APK="colony_1.1.7.apk"

cp $APK_PATH .
rm app-universal-release-unsigned-aligned.apk
~/Android/Sdk/build-tools/34.0.0/zipalign -v 4 app-universal-release-unsigned.apk app-universal-release-unsigned-aligned.apk
#~/Android/Sdk/build-tools/34.0.0/apksigner sign --ks colony-release.keystore --ks-key-alias colony-release --out colony_1.1.5.apk app-universal-release-unsigned-aligned.apk
~/Android/Sdk/build-tools/34.0.0/apksigner sign --ks $KEYSTORE_PATH --ks-key-alias $KEY_ALIAS --out $OUTPUT_APK app-universal-release-unsigned-aligned.apk

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Build completed successfully!"
    echo "Target: $TARGET"
    echo "Binary location: target/$TARGET/debug/"
else
    echo ""
    echo "❌ Build failed. Check the output above for errors."
    exit 1
fi
